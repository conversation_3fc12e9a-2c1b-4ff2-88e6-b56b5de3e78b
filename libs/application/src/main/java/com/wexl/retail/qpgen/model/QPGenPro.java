package com.wexl.retail.qpgen.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Model;
import com.wexl.retail.qpgen.dto.QPGenProV2Dto;
import com.wexl.retail.qpgen.dto.QpGenStatus;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qp_gen_pro")
public class QPGenPro extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;
  private Long marks;
  private Long duration;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("board_slug")
  private String boardSlug;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("grade_slug")
  private String gradeSlug;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("subject_name")
  private String subjectName;

  private QpGenStatus status;

  @JsonProperty("test_definition_id")
  private Long testDefinitionId;

  @Column(name = "chapter_name")
  private List<String> chapterName;

  @Column(name = "chapter_slug")
  private List<String> chapterSlug;

  @ManyToOne
  @JoinColumn(name = "blueprint_id")
  private BluePrint bluePrint;

  @Column(name = "created_by")
  private Long createdByTeacher;

  @Column(name = "review_request_by")
  private Long reviewByTeacher;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private List<QPGenProV2Dto.QuestionSummaryResponse> summary;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb", name = "metadata")
  private QPGenProV2Dto.Metadata metaData;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb", name = "question_chapter_selections")
  private List<QPGenProV2Dto.QuestionChapterSelectionResponse> questionChapterSelectionResponses;
}
