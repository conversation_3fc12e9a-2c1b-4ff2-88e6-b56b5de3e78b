package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ManagerTestReports extends AbstractMetricHandler implements MetricHandler {
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Long fromDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(FROM_DATE))
            .map(Long.class::cast)
            .orElse(null);
    Long endDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(TO_DATE))
            .map(Long.class::cast)
            .orElse(null);
    String authUserIds =
        Optional.ofNullable(genericMetricRequest.getInput().get(AUTHUSERID))
            .map(Object::toString)
            .orElse(null);
    String orgSlugs =
        Optional.ofNullable(genericMetricRequest.getInput().get(ORG_KEY))
            .map(Object::toString)
            .orElse(null);
    return testDefinitionService.getManagerTestReports(orgSlugs, authUserIds, fromDate, endDate);
  }

  @Override
  public String name() {
    return "manager-test-reports";
  }
}
