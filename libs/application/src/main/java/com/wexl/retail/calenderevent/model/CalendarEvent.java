package com.wexl.retail.calenderevent.model;

import com.wexl.retail.calenderevent.dto.CalenderEventType;
import com.wexl.retail.calenderevent.dto.CalenderEventVisibility;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "calender_events")
public class CalendarEvent extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;

  @Column(name = "due_date")
  private LocalDateTime date;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private List<String> gradeSlug;

  @Column(name = "section_uuid")
  private List<String> sectionUuid;

  private CalenderEventType type;
  private CalenderEventVisibility visibility;

  @Column(name = "academic_year_slug")
  private String academicYearSlug;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private List<String> attachments;

  @OneToMany(mappedBy = "calendarEvent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private List<CalendarEventUser> calendarEventUsers;

  private String colour;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "asset_slug")
  private String assetSlug;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private List<String> assets;

  @Column(name = "notification_id")
  private Long notificationId;
}
