package com.wexl.retail.student.auth;

import static com.wexl.retail.guardian.model.GuardianRole.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.model.*;
import com.wexl.retail.organization.admin.StudentPromotionRequest;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.otp.OtpServiceLegacy;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.publisher.AssociateStudentSectionEventPublisher;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.publisher.StudentPromotionPublisher;
import com.wexl.retail.student.studentpublisher.StudentCreationEventPublisher;
import com.wexl.retail.student.subject.profiles.domain.StudentSubjectProfile;
import com.wexl.retail.student.subject.profiles.repository.StudentSubjectProfilesRepository;
import com.wexl.retail.student.subject.profiles.repository.SubjectProfilesRepository;
import com.wexl.retail.student.subject.profiles.service.SubjectProfilesService;
import com.wexl.retail.util.StrapiService;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class StudentAuthService {

  private final CourseDefinitionRepository courseDefinitionRepository;

  private final ClassroomRepository classroomRepository;

  public static final String INDIA = "IN";
  private static final String INVALID_PASSWORD_MESSAGE = "Invalid password";
  private static final String STUDENT_NOT_FOUND_ERROR = "error.StudentNotFound";
  public static final String BET_ORGSLUG = "bha215263";

  private final UserRepository userRepository;
  private final UserIdpService userIdpService;
  private final StudentRepository studentRepository;
  private final OtpServiceLegacy otpServiceLegacy;
  private final StudentCreationEventPublisher studentCreationEventPublisher;
  private final StudentAuthTransformer studentAuthTransformer;
  private final StrapiService strapiService;
  private final SectionService sectionService;
  private final SectionRepository sectionRepository;
  private final SubjectProfilesService subjectProfileService;
  private final SubjectProfilesRepository subjectProfilesRepository;
  private final OrganizationRepository organizationRepository;

  private final PasswordEncoder passwordEncoder;

  private final StudentSubjectProfilesRepository studentSubjectProfilesRepository;

  private final ContentService contentService;
  private final RoleTemplateService roleTemplateService;
  private final AssociateStudentSectionEventPublisher associateStudentSectionEventPublisher;
  private final StudentPromotionPublisher studentPromotionPublisher;

  private final List<EntityHandler<Student>> studentHandler;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Transactional
  public BulkStudentSignup createStudentsInBulk(@Valid StudentSignupRequest studentSignupRequest) {

    var response = studentAuthTransformer.mapStudentSignupToBulkStudentSignup(studentSignupRequest);
    try {
      String orgSlug =
          strapiService.getOrganizationBySlug(studentSignupRequest.getOrgSlug()).getSlug();
      validateSubjectProfile(studentSignupRequest.classId, studentSignupRequest.boardId, orgSlug);
      createStudentInBulk(studentSignupRequest, orgSlug);
      response.setImportErrorMessage("");
      response.setImportStatus("COMPLETED");
    } catch (Exception exception) {
      response.setImportErrorMessage(exception.getMessage());
      response.setImportStatus("FAILED");
    }

    return response;
  }

  private void validateSubjectProfile(Integer classId, Integer boardId, String orgSlug) {
    final Grade grade = contentService.getGradeById(classId);
    final Entity board = strapiService.getEduBoardById(boardId);
    List<Long> subjectProfileId =
        subjectProfilesRepository.getSubjectProfilesByBoardGradeOrg(
            board.getSlug(), grade.getSlug(), orgSlug);
    if (subjectProfileId.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SubjectProfiles.Configuration");
    }
  }

  public Student createStudent(StudentRequest studentRequest, String orgSlug) {
    if (!isValidPassword(studentRequest.getPassword())) {
      log.error(INVALID_PASSWORD_MESSAGE);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPassword");
    }

    studentRequest.setOrgSlug(orgSlug);
    var studentSignupRequest = studentAuthTransformer.mapStudentSignupRequest(studentRequest);
    return createStudentInBulk(studentSignupRequest, orgSlug);
  }

  @Transactional
  public StudentResponse createOrgStudent(StudentRequest studentRequest, String orgSlug) {

    final var student = createStudent(studentRequest, orgSlug);

    return studentAuthTransformer.studentResponseFrom(
        student.getUserInfo(),
        studentRequest.getGradeSlug(),
        studentRequest.getBoardSlug(),
        studentRequest.getSchoolName(),
        studentRequest.getRollNumber(),
        studentRequest.getClassRollNumber(),
        studentRequest.getAcademicYearSlug(),
        studentRequest.getSection(),
        "");
  }

  public User verifyEmailOtp(OtpVerificationRequest verificationRequest) {

    var student = userRepository.getById(verificationRequest.getUserId());
    boolean otpStatus =
        otpServiceLegacy.verifyOtp(verificationRequest.getOtp(), verificationRequest.getOtpId());
    if (otpStatus) {
      return markStudentVerified(student);
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OTP");
    }
  }

  private User markStudentVerified(User student) {
    if (student.getVerificationStatus().equals(UserVerificationStatus.PENDING)) {
      student.setVerificationStatus(UserVerificationStatus.VERIFIED);
      student = userRepository.save(student);
    }
    return student;
  }

  @Transactional
  public Student createStudentInBulk(StudentSignupRequest studentSignupRequest, String orgSlug) {
    var oldUser = userRepository.findByAuthUserId(studentSignupRequest.getUserName());
    if (oldUser.isPresent()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UsernameExists");
    }
    var section =
        sectionService.getSectionByNameAndOrg(
            studentSignupRequest.section, studentSignupRequest.orgSlug);
    if (section == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SectionNotFound",
          new String[] {studentSignupRequest.section});
    }
    validateSubjectProfile(
        studentSignupRequest.getClassId(), studentSignupRequest.getBoardId(), orgSlug);
    String username = studentSignupRequest.getUserName();
    var studentDetails =
        studentAuthTransformer.mapStudentSignupRequest(
            studentSignupRequest, username, UserVerificationStatus.VERIFIED, orgSlug);

    Student crStudent = null;
    var user = userRepository.findByAuthUserId(studentSignupRequest.crStudentUserName);
    if (user.isPresent()) {
      crStudent = studentRepository.findByUserId(user.get().getId());
    }

    var studentUser = studentDetails.getUserInfo();
    studentUser.setOrganization(orgSlug);
    if (!Objects.equals(orgSlug, BET_ORGSLUG)) {
      studentUser.setEmailVerified(true);
    }
    studentUser.setMobileNumber(studentSignupRequest.getMobileNumber());
    studentUser.setPassword(passwordEncoder.encode(studentSignupRequest.getPassword()));
    studentUser = userRepository.save(studentUser);
    studentUser.setExternalRef(studentSignupRequest.getExternalRef());
    studentDetails.setUserInfo(studentUser);
    studentDetails.setSection(section);
    studentDetails.setClassRollNumber(studentSignupRequest.getClassRollNumber());
    studentDetails.setRollNumber(studentSignupRequest.getRollNumber());
    studentDetails.setCrStudentAuthUserId(crStudent);
    studentDetails.setAttributes(studentSignupRequest.getAttributes());
    studentDetails.setRoleTemplate(studentSignupRequest.getRoleTemplate());
    studentDetails = studentRepository.save(studentDetails);
    mapSubjectProfileWhileCreatingStudent(
        studentUser, studentSignupRequest.classId, studentSignupRequest.boardId, orgSlug);
    Student finalStudentDetails = studentDetails;
    studentHandler.forEach(handler -> handler.postSave(finalStudentDetails));
    studentCreationEventPublisher.studentCreation(finalStudentDetails);
    return studentDetails;
  }

  private void mapSubjectProfileWhileCreatingStudent(
      User user, Integer classId, Integer boardId, String orgSlug) {
    final Grade grade = contentService.getGradeById(classId);
    final Entity board = strapiService.getEduBoardById(boardId);
    List<Long> subjectProfileId =
        subjectProfilesRepository.getSubjectProfilesByBoardGradeOrg(
            board.getSlug(), grade.getSlug(), orgSlug);
    if (subjectProfileId.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SubjectProfiles.Configuration");
    }
    subjectProfileService.mapSubjectProfileToStudent(
        orgSlug, user.getUserName(), subjectProfileId.getFirst());
  }

  private void associateStudentToSection(Student student, String sectionName, String orgSlug) {
    if (sectionName == null
        || sectionName.isEmpty()
        || Objects.equals(student.getSection().getName(), sectionName)) {
      return;
    }
    var section = sectionService.getSectionByNameAndOrg(sectionName, orgSlug);
    if (section != null) {
      student.setSection(section);
    }
    associateStudentSectionEventPublisher.publishEvent(student);
  }

  public StudentResponse getStudent(String orgId, String studentId) {
    final var studentUser = userRepository.getUserByAuthUserId(studentId);
    if (isNull(studentUser) || !orgId.equals(studentUser.getOrganization())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentFind.AuthUserID",
          new String[] {studentId});
    }
    final Optional<Student> optionalStudent = studentRepository.findByUserInfo(studentUser);
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND_ERROR);
    }
    var student = optionalStudent.get();
    final var studentInfo = student.getUserInfo();
    final Grade grade = contentService.getGradeById(student.getClassId());
    final Entity board = strapiService.getEduBoardById(student.getBoardId());
    Section firstSection = student.getSection();
    String sectionUuid = firstSection != null ? firstSection.getName() : "";

    var studentResponse =
        studentAuthTransformer.studentResponseFrom(
            studentInfo,
            grade.getSlug(),
            board.getSlug(),
            student.getSchoolName(),
            student.getRollNumber(),
            student.getClassRollNumber(),
            student.getAcademicYearSlug(),
            sectionUuid,
            studentUser.getGuid());
    studentResponse.setIsFeePaid(
        Objects.isNull(student.getFeeDefaulter()) || !student.getFeeDefaulter());
    return studentResponse;
  }

  public StudentResponse editStudent(
      String orgId, String studentId, StudentRequest studentRequest) {
    final var studentUser = userRepository.getUserByAuthUserId(studentId);

    return editStudent(orgId, studentRequest, studentUser);
  }

  public void updateRollNumber(String orgSlug, String authUserId, String rollNumber) {
    try {
      var user = userRepository.findByAuthUserIdAndOrganization(authUserId, orgSlug);
      if (user == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
      }
      final Student student = user.getStudentInfo();
      boolean exists =
          studentRepository.existsByClassRollNumberAndSection(rollNumber, student.getSection());
      if (exists) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.Students.RollNumberExists",
            new String[] {rollNumber});
      }
      student.setClassRollNumber(rollNumber);
      studentRepository.save(student);
    } catch (Exception e) {
      throw e;
    }
  }

  public void updateStudentPassword(String orgSlug, String authUserId, String newPassword) {
    var user = userRepository.findByAuthUserIdAndOrganization(authUserId, orgSlug);
    if (user == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    user.setPassword(passwordEncoder.encode(newPassword));
    userRepository.save(user);
  }

  public void updateExternalRef(String orgSlug, String authUserId, String externalRef) {
    var user = userRepository.findByAuthUserIdAndOrganization(authUserId, orgSlug);
    if (user == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    user.setExternalRef(externalRef);
    userRepository.save(user);
  }

  @Transactional
  protected StudentResponse editStudent(
      String orgId, StudentRequest studentRequest, User studentUser) {
    if (isNull(studentUser)
        || !orgId.equals(studentUser.getOrganization())
        || !AuthUtil.isStudent(studentUser)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }

    final Optional<Student> optionalStudent = studentRepository.findByUserInfo(studentUser);
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND_ERROR);
    }

    if (isValidPassword(studentRequest.getPassword())) {
      userIdpService.adminSetUserPassword(
          studentUser.getAuthUserId(), studentRequest.getPassword());
    }
    Student crStudent = null;
    var user = userRepository.findByAuthUserId(studentRequest.getCrStudentUserName());
    if (!user.isEmpty()) {
      crStudent = studentRepository.findByUserId(user.get().getId());
    }
    var student = optionalStudent.get();
    var roleTemplate =
        roleTemplateService.getRoleTemplateById(studentRequest.getRoleTemplate().getId());
    int classId = contentService.getGradeBySlug(studentRequest.getGradeSlug()).getId();
    student.setClassId(classId);
    student.setRollNumber(studentRequest.getRollNumber());
    student.setClassRollNumber(
        student.getClassRollNumber() != null
            ? student.getClassRollNumber()
            : studentRequest.getClassRollNumber());
    student.setCrStudentAuthUserId(crStudent);
    student.setRoleTemplate(roleTemplate);
    student.setAttributes(studentRequest.getAttributes());
    student.setFeeDefaulter(!studentRequest.isFeePaid());
    if (nonNull(studentRequest.getSection())) {
      associateStudentToSection(student, studentRequest.getSection(), orgId);
    }
    final var updatedStudent = studentRepository.save(student);
    if (student.getClassId() != classId) {
      List<StudentSubjectProfile> subjectProfiles =
          studentSubjectProfilesRepository.findAllByStudentAndDeletedAtIsNull(student);
      studentSubjectProfilesRepository.deleteAll(subjectProfiles);
      mapSubjectProfileWhileCreatingStudent(
          student.getUserInfo(), classId, student.getBoardId(), orgId);
      studentPromotionPublisher.publishEvent(student);
    }

    studentUser.setFirstName(studentRequest.getFirstName());
    studentUser.setLastName(studentRequest.getLastName());
    studentUser.setEmail(studentRequest.getEmail());
    studentUser.setGender(studentRequest.getGender());
    studentUser.setGuid(studentRequest.getGuid());
    studentUser.setMobileNumber(studentRequest.getMobileNumber());
    studentUser.setStudentInfo(student);

    userRepository.save(studentUser);

    return studentAuthTransformer.studentResponseFrom(
        studentUser,
        studentRequest.getGradeSlug(),
        studentRequest.getBoardSlug(),
        updatedStudent.getSchoolName(),
        updatedStudent.getRollNumber(),
        updatedStudent.getClassRollNumber(),
        updatedStudent.getAcademicYearSlug(),
        studentRequest.getSection(),
        studentUser.getGuid());
  }

  public void deleteStudent(String orgId, String studentId) {
    final var studentUser = userRepository.getUserByAuthUserId(studentId);
    if (isNull(studentUser) || !orgId.equals(studentUser.getOrganization())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND_ERROR);
    }
    studentUser.setIsDeleted(true);
    studentUser.setDeletedAt(new Date());
    userRepository.save(studentUser);
  }

  public List<StudentResponse> getFilteredStudents(
      String orgId, String boardSlug, String gradeSlug, String sectionUuid) {
    try {
      List<StudentResponse> studentResponses = new ArrayList<>();
      List<Student> studentList = new ArrayList<>();
      var board = strapiService.getEduBoardBySlug(boardSlug);
      if (sectionUuid != null) {
        Section section =
            sectionRepository
                .findByUuid(UUID.fromString(sectionUuid))
                .orElseThrow(
                    () ->
                        new ApiException(
                            InternalErrorCodes.INVALID_REQUEST,
                            "error.SectionNotFound",
                            new String[] {sectionUuid}));
        studentList.addAll(
            studentRepository.getListStudentsBySections(Collections.singletonList(section)));
      } else if (boardSlug != null && gradeSlug != null) {
        var grade = contentService.getGradeBySlug(gradeSlug);
        studentList.addAll(
            studentRepository.getStudentsByOrgSlugAndBoardAndGrade(
                orgId,
                Long.valueOf(board.getId()),
                Collections.singletonList(Long.valueOf(grade.getId()))));
      } else {
        studentList.addAll(
            studentRepository.getStudentsByOrgSlugAndBoardAndGrade(
                orgId, Long.valueOf(board.getId()), null));
      }
      studentList.forEach(
          student ->
              studentResponses.add(studentAuthTransformer.getFilterStudentResponse(student)));
      return studentResponses;
    } catch (ApiException e) {
      log.error("Failed to get students", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsGet.Failed");
    }
  }

  public List<StudentResponse> studentsFor(String organization) {
    try {
      final List<OrgStudent> students = studentRepository.findAllStudents(organization, null);

      return studentAuthTransformer.studentsResponseFrom(organization, students);
    } catch (ApiException e) {
      log.error("Failed to get students", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsGet.Failed");
    }
  }

  public List<StudentResponse> allStudents(String organization, Long gradeId) {
    try {
      var grades = strapiService.getAllGrades();
      var boards = strapiService.getAllBoards();
      List<StudentResponse> responses = new ArrayList<>();
      final List<OrgStudent> students = studentRepository.findAllStudents(organization, gradeId);
      var org = organizationRepository.findBySlug(organization);
      for (OrgStudent student : students) {
        responses.add(
            studentAuthTransformer.getStudentResponse(org.getName(), grades, boards, student));
      }
      responses.sort(
          Comparator.comparing(
              StudentResponse::getClassRollNumber,
              Comparator.nullsLast(String::compareToIgnoreCase)));
      return responses;
    } catch (ApiException e) {
      log.error("Failed to get students", e);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentsGet.Failed");
    }
  }

  public void undeleteStudent(String orgId, String studentId) {
    final Optional<User> optionalStudent = userRepository.findByAuthUserId(studentId);

    if (optionalStudent.isEmpty() || !orgId.equals(optionalStudent.get().getOrganization())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND_ERROR);
    }

    var studentUser = optionalStudent.get();
    studentUser.setIsDeleted(null);
    studentUser.setDeletedAt(null);
    studentUser.getStudentInfo().setDeletedAt(null);

    userRepository.save(studentUser);
  }

  private boolean isValidPassword(String password) {
    return password != null && password.length() > 1;
  }

  public void deleteStudent(String studentAuthId) {
    userRepository.deleteStudent(studentAuthId);
  }

  private void validateStudentPromotion(String orgSlug, Grade grade, User user, Student student) {
    if (!orgSlug.equals(user.getOrganization())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.studentPromote",
          new String[] {user.getFirstName()});
    }
    if (student.getClassId() == grade.getId()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentAlreadyPromoted",
          new String[] {grade.getName()});
    }
    final List<Classroom> classrooms =
        classroomRepository.findByStudentsAndDeletedAtIsNullOrderByCreatedAtDesc(student);
    if (!classrooms.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.cannotPromoteClassroomStudents");
    }

    final List<CourseDefinition> courses =
        courseDefinitionRepository.getCourseForStudent(user.getId());
    if (!courses.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.cannotPromoteCourseEnrolledStudents");
    }
  }

  @Transactional
  public void promoteStudent(
      String orgSlug, String studentAuthId, StudentPromotionRequest studentPromotionRequest) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());
    Grade grade = contentService.getGradeBySlug(studentPromotionRequest.getGradeSlug());
    Entity board = strapiService.getEduBoardBySlug(studentPromotionRequest.getBoardSlug());
    var section = validateSection(orgSlug, studentPromotionRequest.getSection());
    validateStudentPromotion(orgSlug, grade, user, student);
    if (Objects.isNull(student.getPrevStudentId())) {
      promotingAsNewStudent(student, studentPromotionRequest, orgSlug, grade, board, section, user);
    } else {
      verifyPreviousStudentPromotions(
          student, studentPromotionRequest, orgSlug, grade, board, section, user);
    }
  }

  private void verifyPreviousStudentPromotions(
      Student presentStudent,
      StudentPromotionRequest studentPromotionRequest,
      String orgSlug,
      Grade grade,
      Entity board,
      Section section,
      User user) {
    Optional<Student> optionalStudent =
        studentRepository.findByIdAndClassId(presentStudent.getPrevStudentId(), grade.getId());

    optionalStudent.ifPresentOrElse(
        student -> reuseExistingStudentForGrade(presentStudent, student, section),
        () ->
            promotingAsNewStudent(
                presentStudent, studentPromotionRequest, orgSlug, grade, board, section, user));
  }

  private void reuseExistingStudentForGrade(
      Student presentStudent, Student previousStudent, Section section) {
    previousStudent.setPrevStudentId(presentStudent.getId());
    previousStudent.setUserInfo(presentStudent.getUserInfo());
    previousStudent.setSection(section);

    presentStudent.setUserInfo(null);
    presentStudent.setSection(sectionRepository.getDefaultSection());
    studentRepository.save(previousStudent);
    var updatedStudent = studentRepository.save(previousStudent);
    studentPromotionPublisher.publishEvent(updatedStudent);
  }

  private void promotingAsNewStudent(
      Student student,
      StudentPromotionRequest studentPromotionRequest,
      String orgSlug,
      Grade grade,
      Entity board,
      Section section,
      User user) {
    validateSubjectProfile(grade.getId(), board.getId(), orgSlug);
    Student newStudent = new Student();
    newStudent.setUserInfo(student.getUserInfo());
    newStudent.setSection(section);
    newStudent.setCrStudentAuthUserId(student.getCrStudentAuthUserId());
    newStudent.setPrevStudentId(student.getId());
    newStudent.setAcademicYearSlug(
        studentPromotionRequest.getAcademicYear() == null
            ? latestAcademicYear
            : studentPromotionRequest.getAcademicYear());
    newStudent.setSchoolName(student.getSchoolName());
    newStudent.setBoardId(board.getId());
    newStudent.setRollNumber(student.getRollNumber());
    newStudent.setClassRollNumber(student.getClassRollNumber());
    newStudent.setClassId(grade.getId());
    newStudent.setRoleTemplate(student.getRoleTemplate());
    student.setUserInfo(null);
    student.setSection(sectionRepository.getDefaultSection());
    studentRepository.save(student);
    var savedStudent = studentRepository.save(newStudent);
    mapSubjectProfileWhileCreatingStudent(user, grade.getId(), board.getId(), orgSlug);
    deleteSubjectProfileforOldStudent(student);
    studentPromotionPublisher.publishEvent(savedStudent);
  }

  private void deleteSubjectProfileforOldStudent(Student student) {
    var studentSubjectProfile = studentSubjectProfilesRepository.findAllByStudent(student);
    studentSubjectProfile.forEach(subjectProfile -> subjectProfile.setDeletedAt(new Date()));
    studentSubjectProfilesRepository.saveAll(studentSubjectProfile);
  }

  public Section validateSection(String orgSlug, String section) {
    var sectionData = sectionService.getSectionByNameAndOrg(section, orgSlug);
    if (sectionData == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound", new String[] {section});
    }
    return sectionData;
  }

  public Student validateStudentByUser(User user) {
    return studentRepository
        .findByUserInfo(user)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND_ERROR));
  }

  public List<StudentResponse> getChildOrgStudentsDetails(List<Organization> Orgs) {
    List<StudentResponse> studentResponses = new ArrayList<>();
    for (Organization org : Orgs) {
      studentResponses.addAll(studentsFor(org.getSlug()));
    }
    return studentResponses;
  }

  public Student validateStudentById(Long studentId) {
    var student = studentRepository.findById(studentId);
    if (student.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PersonDoesntExist");
    }
    return student.get();
  }

  public GuardianDto.StudentGuardianResponse getGuardians(Student student) {
    var primaryGuardian = !student.getGuardians().isEmpty() ? getPrimaryGuardian(student) : null;
    return GuardianDto.StudentGuardianResponse.builder()
        .mobileNumber(Objects.nonNull(primaryGuardian) ? primaryGuardian.getMobileNumber() : null)
        .mothersMobileNumber(getMobileNumber(MOTHER, student.getGuardians()))
        .fatherName(getParentDetails(FATHER, student.getGuardians()))
        .motherName(getParentDetails(MOTHER, student.getGuardians()))
        .guardianName(getParentDetails(GUARDIAN, student.getGuardians()))
        .build();
  }

  private String getMobileNumber(GuardianRole role, List<Guardian> guardians) {
    if (guardians == null || guardians.isEmpty()) {
      return null;
    }

    return guardians.stream()
        .filter(
            guardian ->
                guardian.getRelationType().equals(role) && guardian.getMobileNumber() != null)
        .map(Guardian::getMobileNumber)
        .findFirst()
        .orElse(null);
  }

  public Guardian getPrimaryGuardian(Student student) {
    var primaryGuardian =
        student.getGuardians().stream()
            .filter(guardian -> Boolean.TRUE.equals(guardian.getIsPrimary()))
            .findAny();
    return primaryGuardian.orElseGet(
        () -> !student.getGuardians().isEmpty() ? student.getGuardians().getFirst() : null);
  }

  private String getParentDetails(GuardianRole role, List<Guardian> guardians) {
    if (guardians.isEmpty()) {
      return null;
    }
    var guardian = guardians.stream().filter(x -> x.getRelationType().equals(role)).findFirst();
    return guardian.map(this::getGuardianName).orElse(null);
  }

  private String getGuardianName(Guardian guardian) {
    if (Objects.isNull(guardian)) {
      return null;
    }
    var firstName =
        Objects.nonNull(guardian.getFirstName()) ? guardian.getFirstName() : StringUtils.EMPTY;
    var lastName =
        Objects.nonNull(guardian.getFirstName()) ? guardian.getLastName() : StringUtils.EMPTY;
    return firstName.concat(StringUtils.SPACE + lastName);
  }
}
