package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.FeeGroupFeeTypeRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeeMasterStudentRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import com.wexl.erp.fees.service.fineType.FineTypeEngine;
import com.wexl.erp.fees.service.rules.RuleDto;
import com.wexl.erp.fees.service.rules.RuleParamType;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.util.ValidationUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeHeadService {

  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;
  private final FeeHeadRepository feeHeadRepository;
  private final FeeMasterStudentRepository feeMasterStudentRepository;
  private final FeeRuleEngine feeRuleEngine;
  private final FineTypeEngine fineTypeEngine;
  private final FeeService feeService;
  private final ValidationUtils validationUtils;
  private final AuditTrailService auditTrailUtils;
  private final AuthService authService;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;

  public void createFeeHeads(UUID feeMasterId, FeeDto.FeeMasterRequest request, String orgSlug) {
    var feeGroup = feeService.getFeeGroupById(request.feeGroupId(), orgSlug);
    var feeMaster = feeService.getFeeMasterById(String.valueOf(feeMasterId), orgSlug);
    var students = feeRuleEngine.getStudentsForRule(buildRuleParam(request, feeMaster, orgSlug));
    var feeGroupFeeTypes =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug);

    if (students.isEmpty()) {
      return;
    }

    students.forEach(
        student -> processStudentFeeHeads(student, feeMaster, feeGroupFeeTypes, orgSlug));

    if (ScopeType.CUSTOM.equals(request.scopeType())) {
      saveFeeMasterStudents(feeMaster, students);
    }
  }

  public void processStudentFeeHeads(
      Student student,
      FeeMaster feeMaster,
      List<FeeGroupFeeType> feeGroupFeeTypes,
      String orgSlug) {
    for (FeeGroupFeeType feeGroupFeeType : feeGroupFeeTypes) {
      var feeHead = buildFeeHead(feeMaster, student, feeGroupFeeType, orgSlug);
      if (!checkIfAlreadyExists(student, feeMaster, feeHead.getFeeType())) {
        feeHeadRepository.save(feeHead);
      }
    }
  }

  private RuleDto.RuleParam buildRuleParam(
      FeeDto.FeeMasterRequest request, FeeMaster feeMaster, String orgSlug) {
    return RuleDto.RuleParam.builder()
        .feeMasterId(feeMaster.getId())
        .orgSlug(orgSlug)
        .paramType(getRuleParamType(request.scopeType()))
        .paramValues(getRuleParamValues(request))
        .build();
  }

  private RuleParamType getRuleParamType(ScopeType scopeType) {
    return switch (scopeType) {
      case SECTION -> RuleParamType.SECTION;
      case GRADE -> RuleParamType.GRADE;
      case SCHOOL -> RuleParamType.SCHOOL;
      case CUSTOM -> RuleParamType.STUDENT_ID;
    };
  }

  private List<String> getRuleParamValues(FeeDto.FeeMasterRequest request) {
    return switch (request.scopeType()) {
      case SECTION -> request.sectionUuid();
      case GRADE -> request.gradeSlug();
      case SCHOOL -> Collections.singletonList("school");
      case CUSTOM -> request.studentId().stream().map(String::valueOf).toList();
    };
  }

  private boolean checkIfAlreadyExists(Student student, FeeMaster feeMaster, FeeType feeType) {
    return feeHeadRepository.existsByFeeMasterAndStudentAndFeeType(feeMaster, student, feeType);
  }

  private void saveFeeMasterStudents(FeeMaster feeMaster, List<Student> students) {
    students.forEach(
        student ->
            feeMasterStudentRepository.save(
                FeeMasterStudent.builder().feeMaster(feeMaster).student(student).build()));
  }

  private FeeHead buildFeeHead(
      FeeMaster feeMaster, Student student, FeeGroupFeeType feeGroupFeeType, String orgSlug) {
    return FeeHead.builder()
        .feeMaster(feeMaster)
        .orgSlug(orgSlug)
        .student(student)
        .amount(feeGroupFeeType.getAmount())
        .balanceAmount(feeGroupFeeType.getAmount())
        .discountAmount(0.0)
        .fineAmount(feeGroupFeeType.getFineAmount())
        .feeType(feeGroupFeeType.getFeeType())
        .dueDate(feeGroupFeeType.getDueDate())
        .status(FeeStatus.UNPAID)
        .build();
  }

  public FeeDto.StudentsFeeHeadResponseData getFeeHeadsByStudent(
      String orgSlug, String authUserId) {
    var user = validationUtils.isValidUser(authUserId);

    var feeHeads =
        feeHeadRepository.findAllByStudentIdAndOrgSlugAndStatusNotIn(
            user.getStudentInfo().getId(), orgSlug, List.of(FeeStatus.VOIDED));

    if (feeHeads.isEmpty()) {
      return FeeDto.StudentsFeeHeadResponseData.builder().build();
    }

    List<FeeDto.StudentsFeeHeadResponse> responses = buildResponses(feeHeads, user);

    Map<String, List<FeeDto.StudentsFeeHeadResponse>> grouped = groupAndSortByQuarter(responses);

    return buildQuarterlyResponse(grouped);
  }

  private List<FeeDto.StudentsFeeHeadResponse> buildResponses(List<FeeHead> feeHeads, User user) {

    return feeHeads.stream()
        .map(
            feeHead -> {
              var student = feeHead.getStudent();
              var section = student.getSection();

              var feeGroupFeeType =
                  feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(
                      feeHead.getFeeMaster().getFeeGroup().getId(), feeHead.getOrgSlug());

              var feeHeadFeeType =
                  feeGroupFeeType.stream()
                      .filter(x -> x.getFeeType().equals(feeHead.getFeeType()))
                      .findFirst()
                      .orElseThrow(
                          () ->
                              new IllegalArgumentException(
                                  "Fee type not found for fee head: " + feeHead.getId()));
              if (feeHead.getDueDate() == null
                  || !feeHeadFeeType
                      .getDueDate()
                      .toLocalDate()
                      .equals(feeHead.getDueDate().toLocalDate())) {
                feeHead.setDueDate(feeHeadFeeType.getDueDate());
                feeHeadRepository.save(feeHead);
              }
              var fineAmount = calculateFineAmount(feeHead, feeHeadFeeType);

              var feePaymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);
              var paidDate =
                  feePaymentDetails.stream()
                      .map(FeePaymentDetail::getCreatedAt)
                      .max(Comparator.naturalOrder())
                      .orElse(null);

              String quarter =
                  feeHead.getDueDate() == null
                      ? "Unknown"
                      : getQuarter(convertIso8601ToEpoch(feeHead.getDueDate()));

              return new FeeDto.StudentsFeeHeadResponse(
                  feeHead.getId().toString(),
                  student.getId(),
                  user.getFirstName() + " " + user.getLastName(),
                  section.getGradeSlug(),
                  getPaymentStatus(feeHead, fineAmount),
                  section.getGradeName(),
                  section.getUuid().toString(),
                  section.getName(),
                  feeHead.getFeeMaster().getId(),
                  feeHead.getAmount(),
                  fineAmount,
                  feeHeadFeeType.getFineType(),
                  feeHead.getDiscountAmount(),
                  feeHead.getPaidAmount(),
                  calculateBalanceAmount(feeHead, fineAmount),
                  feeHead.getDueDate() == null ? null : convertIso8601ToEpoch(feeHead.getDueDate()),
                  feeHead.getFeeType().getId(),
                  feeHead.getFeeType().getCode(),
                  feeHead.getFeeType().getName(),
                  feeHead.getFeeType().getDescription(),
                  feeHead.getFeeMaster().getFeeGroup().getId().toString(),
                  feeHead.getFeeMaster().getFeeGroup().getName(),
                  convertIso8601ToEpoch(feeHead.getCreatedAt().toLocalDateTime()),
                  quarter,
                  paidDate == null ? null : convertIso8601ToEpoch(paidDate.toLocalDateTime()));
            })
        .toList();
  }

  private String getQuarter(Long dueDate) {
    int month =
        LocalDate.ofInstant(Instant.ofEpochMilli(dueDate), ZoneId.systemDefault()).getMonthValue();

    if (month <= 3) return "q1_january_to_march";
    else if (month <= 6) return "q2_april_to_june";
    else if (month <= 9) return "q3_july_to_september";
    else return "q4_october_to_december";
  }

  private Double calculateBalanceAmount(FeeHead feeHead, Double fineAmount) {
    return (feeHead.getAmount() != null ? feeHead.getAmount() : 0.0)
        - (feeHead.getDiscountAmount() != null && feeHead.getDiscountAmount() > 0
            ? feeHead.getDiscountAmount()
            : 0.0)
        - (feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        + fineAmount;
  }

  private Map<String, List<FeeDto.StudentsFeeHeadResponse>> groupAndSortByQuarter(
      List<FeeDto.StudentsFeeHeadResponse> responses) {

    Map<String, List<FeeDto.StudentsFeeHeadResponse>> grouped =
        responses.stream().collect(Collectors.groupingBy(r -> getQuarter(r.dueDate())));

    grouped.replaceAll(
        (q, list) ->
            list.stream()
                .sorted(
                    Comparator.comparing(
                        r ->
                            LocalDate.ofInstant(
                                    Instant.ofEpochMilli(r.dueDate()), ZoneId.systemDefault())
                                .getMonthValue()))
                .toList());

    return grouped;
  }

  private FeeDto.StudentsFeeHeadResponseData buildQuarterlyResponse(
      Map<String, List<FeeDto.StudentsFeeHeadResponse>> grouped) {

    return FeeDto.StudentsFeeHeadResponseData.builder()
        .q1(grouped.getOrDefault("q1_january_to_march", List.of()))
        .q2(grouped.getOrDefault("q2_april_to_june", List.of()))
        .q3(grouped.getOrDefault("q3_july_to_september", List.of()))
        .q4(grouped.getOrDefault("q4_october_to_december", List.of()))
        .build();
  }

  private FeeStatus getPaymentStatus(FeeHead feeHead, Double fineAmount) {
    double amount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
    double discountAmount = feeHead.getDiscountAmount() != null ? feeHead.getDiscountAmount() : 0.0;
    double totalAmount = amount + fineAmount - discountAmount;
    double paidAmount = feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0;
    double balanceAmount =
        feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : totalAmount - paidAmount;

    if (balanceAmount == 0) {
      return FeeStatus.PAID;
    } else if (paidAmount == 0) {
      return FeeStatus.UNPAID;
    } else if (paidAmount < totalAmount) {
      return FeeStatus.PARTIALLY_PAID;
    }

    return FeeStatus.UNPAID;
  }

  public Double calculateFineAmount(FeeHead feeHead, FeeGroupFeeType feeHeadFeeType) {
    return fineTypeEngine.getFineAmount(feeHead, feeHeadFeeType);
  }

  public void saveFeeMasterStudents(String orgSlug, String studentAuthId, String feeMasterId) {
    var user = validationUtils.isValidUser(studentAuthId);

    var feeMaster = feeService.getFeeMasterById(feeMasterId, orgSlug);
    var request =
        FeeDto.FeeMasterRequest.builder()
            .studentId(Collections.singletonList(user.getStudentInfo().getId()))
            .scopeType(ScopeType.CUSTOM)
            .build();
    createFeeHeads(feeMaster.getId(), request, orgSlug);
  }

  public FeeHead saveFeeHead(
      Student student,
      FeeMaster feeMaster,
      FeeType feeType,
      String orgSlug,
      FeeGroup feeGroup,
      Concession concession) {
    var feeGroupFeeType =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug).stream()
            .filter(f -> f.getFeeType().equals(feeType))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Fee type not found in fee group"));

    var amount = feeGroupFeeType.getAmount();
    var fineAmount = feeGroupFeeType.getFineAmount();
    var dueDate = feeGroupFeeType.getDueDate();

    FeeHead feeHead = new FeeHead();
    feeHead.setFeeMaster(feeMaster);
    feeHead.setOrgSlug(orgSlug);
    feeHead.setStudent(student);
    feeHead.setAmount(amount);
    feeHead.setBalanceAmount(amount);
    feeHead.setDiscountAmount(getDiscountAmount(concession, feeHead));
    feeHead.setFineAmount(fineAmount);
    feeHead.setFeeType(feeType);
    feeHead.setDueDate(dueDate);
    feeHead.setStatus(FeeStatus.UNPAID);
    feeHead.setConcession(concession);

    return feeHeadRepository.save(feeHead);
  }

  public double getDiscountAmount(Concession concession, FeeHead feeHead) {
    if (concession == null
        || feeHead == null
        || concession.getValue() == null
        || feeHead.getAmount() == null) {
      return 0.0;
    }

    if (ConcessionType.PERCENTAGE.equals(concession.getType())) {
      if (concession.getValue() < 0 || concession.getValue() > 100) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Concession.InvalidPercentage");
      }

      double discount = feeHead.getAmount() * concession.getValue() / 100;
      return Math.round(discount * 100.0) / 100.0;
    }

    return concession.getValue();
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    return feeHeadRepository
        .findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Fee head not found: " + feeHeadId));
  }

  public void voidFeeHead(String orgSlug, String feeHeadId) {
    var feeHeadData = getFeeHeadById(feeHeadId, orgSlug);
    var oldStatus = feeHeadData.getStatus();

    feeHeadData.setStatus(FeeStatus.VOIDED);
    feeHeadData.setDueDate(null);
    feeHeadData.setBalanceAmount(0.0);
    feeHeadData.setConcession(null);
    feeHeadData.setDiscountAmount(0.0);

    feeHeadRepository.save(feeHeadData);

    auditTrailUtils.log(
        String.valueOf(feeHeadData.getId()),
        "Fee Head",
        "Status Update",
        oldStatus.toString(),
        FeeStatus.VOIDED.toString(),
        orgSlug,
        authService.getUserDetails().getId());
  }
}
